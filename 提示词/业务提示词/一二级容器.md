# 一二级容器

## 一级容器

**1.主容器布局(main_container.tsx):**

- 1.全视窗高度:100%
- 2.全视窗宽度:100%
- 3.背景颜色:#242424
- 4.展示方式:弹性布局
- 5.主轴方向:水平
- 6.对齐方式:水平，垂直居中
- 7.溢出处理:隐藏
- 8.滚动行为:移除

## 二级容器

**1.布局结构说明:**

- 1.排列方式:二级容器在主容器内水平排列
- 2.排列结构:[二级容器布局1] [二级容器布局2或3]
- 3.容器间隔:通过[间隔]产生间隔

**2.二级容器布局1(componetA1.tsx):**

- 1.容器形状:矩形
- 2.视窗高度:全视窗高度99%
- 3.视窗宽度:全视窗高度99%
- 4.背景颜色:#6d6d6d
- 5.展示方式:弹性布局
- 6.弹性方向:垂直
- 7.对齐方式:水平，垂直居中
- 8.溢出处理:隐藏

**3.包装容器(componet_interactionB.tsx):**

- 1.包装容器:
  - 1.包装对象:'componetB1'和'componetB2'
  - 2.包装作用:应用间隔和作为‘componet4’的定位参考
- 2.间隔:
  - 1.间隔属性:‘margin-left: 1vw’
  - 2.间隔对象:'componetB1'，'componetB2'
- 3.容器位置:
  - 1.容器定位:相对位置
- 4.容器尺寸:
  - 1.高度:全视窗高度的99%
  - 2.宽度:全视窗宽度的20%

**4.二级容器布局2(componetB1.tsx):**

- 1.容器形状:长方形
- 2.容器尺寸:继承包装容器的100%宽高
- 3.背景颜色: #6d6d6d
- 4.展示方式:弹性布局
- 5.弹性方向:垂直居中
- 6.溢出处理:隐藏

**5.二级容器布局3(componetB2.tsx):**

- 1.容器形状:长方形
- 2.容器尺寸:继承包装容器的100%宽高
- 3.背景颜色: #b6b6b6
- 4.展示方式:弹性布局
- 5.弹性方向:垂直居中
- 6.溢出处理:隐藏

**6.二级容器布局4(componetButton.tsx):**

- 1.容器说明:用于放置切换按键，悬浮于‘componetB1’和‘componetB2’区域上
- 2.视窗高度:全视窗高度的3%
- 3.视窗宽度:全视窗宽度的20%
- 4.背景颜色:无
- 5.展示方式:弹性布局
- 6.弹性方向:水平居中
- 7.溢出方式:隐藏
- 8.容器位置:绝对位置
- 9.顶部对齐:‘top: 0’
- 10.左部对齐:'left: 0'

**7.按键调用(componetButton.tsx):**

- 1.模式按键:
  - 1.调用按键'SecondaryButton'
    - 1.配置参数:
      - 1.键高:占'componetButton'容器高的100%
      - 2.键宽:占'componetButton'容器宽的50%
    - 2.文本:
      - 1.键内文本:'模式'
      - 2.文本大小:尺寸自适应
- 2.业务按键:
  - 1.调用按键‘SecondaryButton’
    - 1.配置参数:
      - 1.键高:占‘componentButton’容器高的100%
      - 2.键宽:占‘componentButton’容器宽的50%
    - 2.文本:
      - 1.键内文本:'业务'
      - 2.文本大小:尺寸自适应

**8.按键业务逻辑(store.ts):**

- 1.状态管理:
  - 1.'模式'按键的`true/false`状态信息存储于‘store.ts’
  - 2.'业务'按键的`true/false`状态信息存储于‘store.ts’
- 3.状态切换:
  - 1.点击‘模式’按键时，将其状态设置为`true`，并将‘业务’按键状态设置为`false`
  - 2.点击‘业务’按键时，将其状态设置为`true`，并将‘模式’按键状态设置为`false`
  - 3.默认状态:为‘模式’按键设置默认激活状态
  - 4.激活状态下该按键应该被禁用

**9.容器交互(componet_interactionB.tsx):**

- 1.容器切换:
  - 1.‘compnetButton’始终保持可见，其位置不受‘compnetB1’和‘componetB2’显隐切换影响
  - 2.当'模式'按键激活时，显示'componetB1'容器，并隐藏'componetB2'容器
  - 3.当'业务'按键激活时，显示'componetB2'容器，并隐藏'componetB1'容器
